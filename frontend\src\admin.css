body {
  margin: 0;
  background: linear-gradient(to right, #6b73ff, #000dff);
  font-family: Arial, sans-serif;
  display: flex;
  justify-content: center;
  align-items: start;
  min-height: 100vh;
  padding: 40px 20px;
}

.prompt-container {
  width: 100%;
  max-width: 900px;
  background: #fff;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
}

.prompt-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.prompt-header h1 {
  font-size: 1.8rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 8px;
}

.top-buttons {
  display: flex;
  gap: 12px;
}

.nav-link,
.logout-button {
  background-color: #f0f0f0;
  border: none;
  color: #333;
  font-weight: bold;
  padding: 8px 14px;
  border-radius: 6px;
  text-decoration: none;
  cursor: pointer;
  transition: background 0.2s ease;
}

.nav-link:hover,
.logout-button:hover {
  background-color: #dcdcdc;
}

.prompt-label {
  font-weight: bold;
  margin-top: 20px;
  display: block;
}

.prompt-select,
.prompt-textarea {
  width: 100%;
  padding: 10px;
  margin-top: 6px;
  border: 1px solid #ccc;
  border-radius: 6px;
  margin-bottom: 16px;
}

.prompt-textarea {
  height: 300px;
  resize: none;
  font-family: monospace;
  font-size: 14px;
}

.prompt-buttons {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

.save-btn {
  background-color: #2563eb;
  color: white;
  font-weight: bold;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.prompt-message {
  margin-top: 20px;
  font-size: 14px;
}
