import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { BACKEND_URL } from "./utils/api";

export default function HierarchicalProductSelection({ token }) {
  const navigate = useNavigate();
  const accessToken = token || localStorage.getItem("access");
  
  const [hierarchy, setHierarchy] = useState({});
  const [selectedPath, setSelectedPath] = useState([]);
  const [currentLevel, setCurrentLevel] = useState({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Load product hierarchy from backend
  useEffect(() => {
    const fetchHierarchy = async () => {
      try {
        const response = await fetch(`${BACKEND_URL}/api/product_hierarchy/`, {
          headers: {
            Authorization: `Bearer ${accessToken}`,
          },
        });

        const data = await response.json();
        if (response.ok) {
          setHierarchy(data.hierarchy);
          setCurrentLevel(data.hierarchy);
        } else {
          setError(data.message || "Failed to load product hierarchy");
        }
      } catch (err) {
        console.error("Error fetching hierarchy:", err);
        setError("Network error. Please check your connection.");
      } finally {
        setLoading(false);
      }
    };

    fetchHierarchy();
  }, [accessToken]);

  const handleSelection = (key) => {
    const newPath = [...selectedPath, key];
    setSelectedPath(newPath);

    // Navigate deeper into the hierarchy
    const nextLevel = currentLevel[key];
    
    if (nextLevel && typeof nextLevel === 'object' && !Array.isArray(nextLevel)) {
      // Check if this is a leaf node (contains models, versions, etc.)
      const hasSubCategories = Object.keys(nextLevel).some(k => 
        typeof nextLevel[k] === 'object' && 
        !Array.isArray(nextLevel[k]) &&
        !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors'].includes(k)
      );

      if (hasSubCategories) {
        setCurrentLevel(nextLevel);
      } else {
        // This is a leaf node, proceed to details form
        proceedToDetailsForm(newPath, nextLevel);
      }
    } else {
      // This shouldn't happen with our hierarchy structure
      proceedToDetailsForm(newPath, {});
    }
  };

  const handleBack = () => {
    if (selectedPath.length === 0) {
      navigate("/actions");
      return;
    }

    const newPath = selectedPath.slice(0, -1);
    setSelectedPath(newPath);

    // Navigate back up the hierarchy
    let level = hierarchy;
    for (const pathItem of newPath) {
      level = level[pathItem];
    }
    setCurrentLevel(level);
  };

  const proceedToDetailsForm = (path, leafData) => {
    // Store the hierarchical selection in sessionStorage for the details form
    const hierarchicalData = {
      path: path,
      leafData: leafData,
      productCategory: path[0] || '',
      productSubcategory: path[1] || '',
      productFamily: path[2] || '',
      productInterface: path[3] || ''
    };
    
    sessionStorage.setItem('hierarchicalSelection', JSON.stringify(hierarchicalData));
    navigate('/new-ticket-details');
  };

  const getBreadcrumb = () => {
    return selectedPath.join(' > ');
  };

  const getCurrentOptions = () => {
    if (!currentLevel || typeof currentLevel !== 'object') return [];
    
    return Object.keys(currentLevel).filter(key => 
      typeof currentLevel[key] === 'object' && 
      !Array.isArray(currentLevel[key]) &&
      !['models', 'versions', 'interfaces', 'platforms', 'types', 'lengths', 'focal_lengths', 'colors'].includes(key)
    );
  };

  if (loading) {
    return (
      <div style={{ 
        padding: "40px", 
        textAlign: "center",
        fontFamily: "Arial, sans-serif"
      }}>
        <h2>Loading Product Categories...</h2>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ 
        padding: "40px", 
        maxWidth: "600px", 
        margin: "0 auto",
        fontFamily: "Arial, sans-serif"
      }}>
        <div style={{
          backgroundColor: "#ffebee",
          color: "#c62828",
          padding: "20px",
          borderRadius: "8px",
          border: "1px solid #ef5350",
          textAlign: "center"
        }}>
          <h3>Error Loading Product Categories</h3>
          <p>{error}</p>
          <button
            onClick={() => navigate("/actions")}
            style={{
              padding: "10px 20px",
              backgroundColor: "#2196F3",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              marginTop: "10px"
            }}
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }

  const currentOptions = getCurrentOptions();

  return (
    <div style={{ 
      padding: "40px", 
      maxWidth: "800px", 
      margin: "0 auto", 
      fontFamily: "Arial, sans-serif"
    }}>
      <h1 style={{ 
        color: "#333", 
        marginBottom: "20px",
        textAlign: "center"
      }}>
        Select Product Category
      </h1>

      {/* Breadcrumb */}
      {selectedPath.length > 0 && (
        <div style={{
          backgroundColor: "#f5f5f5",
          padding: "10px 15px",
          borderRadius: "4px",
          marginBottom: "20px",
          fontSize: "14px",
          color: "#666"
        }}>
          <strong>Selected:</strong> {getBreadcrumb()}
        </div>
      )}

      <p style={{
        fontSize: "1.1rem",
        color: "#666",
        marginBottom: "30px",
        textAlign: "center"
      }}>
        {selectedPath.length === 0 
          ? "Please select your product type to begin:"
          : `Please select the ${selectedPath.length === 1 ? 'subcategory' : selectedPath.length === 2 ? 'product family' : 'interface type'}:`
        }
      </p>

      {/* Product Options */}
      <div style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
        gap: "20px",
        marginBottom: "30px"
      }}>
        {currentOptions.map((option) => (
          <button
            key={option}
            onClick={() => handleSelection(option)}
            style={{
              padding: "20px",
              border: "2px solid #ddd",
              borderRadius: "8px",
              backgroundColor: "white",
              cursor: "pointer",
              fontSize: "16px",
              fontWeight: "bold",
              color: "#333",
              transition: "all 0.2s ease",
              textAlign: "center",
              minHeight: "80px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center"
            }}
            onMouseOver={(e) => {
              e.target.style.borderColor = "#4CAF50";
              e.target.style.backgroundColor = "#f8fff8";
              e.target.style.transform = "translateY(-2px)";
              e.target.style.boxShadow = "0 4px 8px rgba(0,0,0,0.1)";
            }}
            onMouseOut={(e) => {
              e.target.style.borderColor = "#ddd";
              e.target.style.backgroundColor = "white";
              e.target.style.transform = "translateY(0)";
              e.target.style.boxShadow = "none";
            }}
          >
            {option}
          </button>
        ))}
      </div>

      {/* Navigation Buttons */}
      <div style={{ 
        display: "flex", 
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <button
          onClick={handleBack}
          style={{
            padding: "12px 24px",
            backgroundColor: "#757575",
            color: "white",
            border: "none",
            borderRadius: "6px",
            fontSize: "16px",
            cursor: "pointer",
            transition: "background-color 0.2s ease"
          }}
          onMouseOver={(e) => e.target.style.backgroundColor = "#616161"}
          onMouseOut={(e) => e.target.style.backgroundColor = "#757575"}
        >
          ← {selectedPath.length === 0 ? "Back to Actions" : "Back"}
        </button>

        <div style={{ 
          fontSize: "14px", 
          color: "#666",
          textAlign: "center"
        }}>
          Step {selectedPath.length + 1} of 4
        </div>
      </div>
    </div>
  );
}
