import mysql.connector
import os

# 🔒 Hardcoded save directory (change this to your desired folder)
save_dir = "C:/Users/<USER>/Downloads"

# ✅ Ensure the directory exists
if not os.path.isdir(save_dir):
    print(f"❌ Directory does not exist: {save_dir}")
    exit()

try:
    # 🔐 Connect to MySQL
    conn = mysql.connector.connect(
        host='localhost',
        user='root',
        password='phoobesh333',
        database='rough'
    )

    cursor = conn.cursor()

    # 📦 Fetch the file using ID
    file_id = 19  # <-- hardcoded file ID
    query = "SELECT file_name, file_data FROM pdf_files WHERE id = %s"
    cursor.execute(query, (file_id,))
    result = cursor.fetchone()

    if result:
        filename, file_data = result

        # 📂 Construct full path
        save_path = os.path.join(save_dir, filename)

        # 💾 Save file
        with open(save_path, 'wb') as f:
            f.write(file_data)

        print(f"✅ File downloaded successfully to:\n{save_path}")
    else:
        print("❌ File not found in the database.")

except mysql.connector.Error as err:
    print("❗ MySQL Error:", err)

finally:
    if conn.is_connected():
        cursor.close()
        conn.close()
