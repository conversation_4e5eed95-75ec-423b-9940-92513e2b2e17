import React, { useState, useEffect } from "react";

export default function PromptAdminPage() {
  const [promptType, setPromptType] = useState("query");
  const [promptText, setPromptText] = useState("");
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");

  const promptTypeMap = {
    query: "chat",
    gptvision: "img2",
  };

  useEffect(() => {
    const fetchPrompt = async () => {
      setLoading(true);
      setError("");
      try {
        const res = await fetch(
          `http://localhost:8000/api/prompts/?type=${promptTypeMap[promptType]}`
        );
        if (!res.ok) throw new Error("Failed to load prompt");
        const data = await res.json();
        setPromptText(data.template || "");
      } catch (err) {
        setError(err.message);
        setPromptText("");
      }
      setLoading(false);
    };
    fetchPrompt();
  }, [promptType]);

  const savePrompt = async () => {
    setSaving(true);
    setError("");
    try {
      const res = await fetch("http://localhost:8000/api/prompts/", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          prompt_type: promptTypeMap[promptType],
          template: promptText,
          is_active: true,
        }),
      });
      if (!res.ok) throw new Error("Failed to save prompt");
      alert("Prompt saved successfully!");
    } catch (err) {
      setError(err.message);
    }
    setSaving(false);
  };

  return (
    <>
      <style>{`
        body {
          margin: 0;
          background: linear-gradient(135deg, #1a1c29 0%, #2e2f3e 100%);
          font-family: Arial, sans-serif;
          display: flex;
          justify-content: center;
          align-items: start;
          min-height: 100vh;
          padding: 40px 20px;
          color: #eee;
        }
        .prompt-container {
          width: 70vw;
          max-width: 1200px;
          background: linear-gradient(145deg, #252836, #1b1d2a);
          padding: 40px 50px;
          border-radius: 16px;
          box-shadow: 0 10px 30px rgba(0,0,0,0.7);
          color: #ddd;
          min-height: 80vh;
          display: flex;
          flex-direction: column;
        }
        .prompt-header {
          display: flex;
          justify-content: flex-start;
          align-items: center;
          margin-bottom: 30px;
        }
        .prompt-header h1 {
          font-size: 2.2rem;
          font-weight: 700;
          color: #f0f0f5;
        }
        .prompt-label {
          font-weight: 600;
          margin-top: 20px;
          display: block;
          color: #bbb;
        }
        .prompt-select,
        .prompt-textarea {
          width: 100%;
          padding: 14px;
          margin-top: 8px;
          border: none;
          border-radius: 10px;
          background: #33394f;
          color: #eee;
          font-size: 1rem;
          font-family: monospace;
          box-shadow: inset 0 0 8px rgba(0,0,0,0.7);
          transition: background 0.3s ease;
        }
        .prompt-select:focus,
        .prompt-textarea:focus {
          outline: none;
          background: #404865;
          box-shadow: inset 0 0 10px #6272ff;
          color: #f8f8f2;
        }
        .prompt-textarea {
          min-height: 350px;
          resize: vertical;
        }
        .prompt-button {
          padding: 14px 28px;
          border: none;
          font-weight: 700;
          border-radius: 10px;
          cursor: pointer;
          background-color: #6272ff;
          color: white;
          font-size: 1.1rem;
          transition: background-color 0.3s ease;
          box-shadow: 0 4px 10px rgba(98, 114, 255, 0.6);
          margin-top: 20px;
          align-self: flex-start;
        }
        .prompt-button:disabled {
          background-color: #444a6d;
          cursor: not-allowed;
          box-shadow: none;
          color: #888;
        }
        .prompt-button:not(:disabled):hover {
          background-color: #7a89ff;
          box-shadow: 0 6px 14px rgba(122, 137, 255, 0.8);
        }
        .prompt-message {
          margin-top: 20px;
          font-size: 14px;
          color: #f55555;
          font-weight: 600;
        }
      `}</style>

      <div className="prompt-container">
        <div className="prompt-header">
          <h1>Prompt Management</h1>
        </div>

        <label className="prompt-label">
          Select prompt type:
          <select
            className="prompt-select"
            value={promptType}
            onChange={(e) => setPromptType(e.target.value)}
            disabled={loading || saving}
          >
            <option value="query">Query (views.py)</option>
            <option value="gptvision">GPT Vision (img2.py)</option>
          </select>
        </label>

        {loading ? (
          <p className="prompt-message" style={{ color: "#eee" }}>
            Loading prompt...
          </p>
        ) : (
          <>
            <textarea
              className="prompt-textarea"
              value={promptText}
              onChange={(e) => setPromptText(e.target.value)}
              disabled={saving}
              spellCheck={false}
            />
            <button
              className="prompt-button"
              onClick={savePrompt}
              disabled={saving || loading || !promptText.trim()}
            >
              {saving ? "Saving..." : "Save Prompt"}
            </button>
            {error && <p className="prompt-message">Error: {error}</p>}
          </>
        )}
      </div>
    </>
  );
}
