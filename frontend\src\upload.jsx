import React, { useState } from 'react';
import axios from 'axios';
import './upload.css'; // Make sure to create this CSS file

const UploadPDF = () => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [uploadStatus, setUploadStatus] = useState("");

  const handleFileChange = (event) => {
    const newFiles = Array.from(event.target.files);
    const uniqueNewFiles = newFiles.filter(file =>
      !selectedFiles.some(f => f.name === file.name)
    );
    setSelectedFiles(prev => [...prev, ...uniqueNewFiles]);
    setUploadStatus("");
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) {
      setUploadStatus("⚠️ Please select PDF files first.");
      return;
    }

    setUploadStatus("📤 Uploading files and processing...");

    const formData = new FormData();
    selectedFiles.forEach(file => {
      formData.append("pdf_file", file);
    });

    try {
      const response = await axios.post('http://localhost:8000/api/upload_pdf/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response.status === 200) {
        const data = response.data;
        if (data.processed_files) {
          setUploadStatus(`✅ ${data.message} - Files processed: ${data.processed_files.join(', ')}`);
        } else if (data.uploaded_files) {
          setUploadStatus(`⚠️ ${data.message} - Uploaded: ${data.uploaded_files.join(', ')}`);
        } else {
          setUploadStatus("✅ All files uploaded and processed successfully.");
        }
        setSelectedFiles([]);
      } else {
        setUploadStatus("⚠️ Upload failed. Try again.");
      }
    } catch (error) {
      console.error("Upload error:", error);
      setUploadStatus("❌ Error uploading files.");
    }
  };

  const handleRemoveFile = (index) => {
    const updatedFiles = [...selectedFiles];
    updatedFiles.splice(index, 1);
    setSelectedFiles(updatedFiles);
  };

  return (
    <div className="upload-container">
      <h1>📄 PDF Upload Manager</h1>

      <label className="upload-label">Select PDF Files:</label>
      <input type="file" accept=".pdf" multiple onChange={handleFileChange} className="upload-input" />

      {selectedFiles.length > 0 && (
        <div className="file-list">
          <h4>🗂️ Selected Files:</h4>
          <ul>
            {selectedFiles.map((file, index) => (
              <li key={index}>
                {file.name}
                <button onClick={() => handleRemoveFile(index)} className="remove-btn">❌ Remove</button>
              </li>
            ))}
          </ul>
        </div>
      )}

      <button onClick={handleUpload} className="upload-button" disabled={selectedFiles.length === 0}>
        🚀 Upload Files
      </button>

      {uploadStatus && <p className="upload-status">{uploadStatus}</p>}
    </div>
  );
};

export default UploadPDF;
