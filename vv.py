import weaviate

# ======== CONFIG ========
WEAVIATE_URL = "http://localhost:8080"  # or your endpoint
API_KEY = None  # for cloud use weaviate.auth.AuthApiKey(api_key="...")
SOURCE_CLASS = "ChunkEmbeddingsV2"      # ✅ case-sensitive
DEST_CLASS = "ChunkEmbeddings"       # ✅ already exists
BATCH_SIZE = 100

# ======== CONNECT ========
client = weaviate.Client(
    url=WEAVIATE_URL,
    # auth_client_secret=weaviate.auth.AuthApiKey(API_KEY),  # for cloud
)

# ======== FETCH ALL OBJECTS FROM SOURCE ========
def fetch_all_objects(class_name):
    results = []
    offset = 0
    while True:
        query = (
            client.query
            .get(class_name, ["_additional { id }"])  # Only fetch IDs
            .with_limit(BATCH_SIZE)
            .with_offset(offset)
        )
        data = query.do()

        # Handle GraphQL errors
        if "errors" in data:
            print("❌ GraphQL Error:", data["errors"])
            break

        if "data" not in data or "Get" not in data["data"]:
            print("❌ Unexpected response:", data)
            break

        objs = data["data"]["Get"].get(class_name, [])
        if not objs:
            break
        results.extend(objs)
        offset += BATCH_SIZE
    return results

print(f"Fetching all objects from {SOURCE_CLASS}...")
source_objects = fetch_all_objects(SOURCE_CLASS)
print(f"Fetched {len(source_objects)} objects.")

# ======== INSERT INTO DESTINATION WITH SAME UUID ========
print(f"Inserting into {DEST_CLASS} with same IDs...")
with client.batch as batch:
    batch.batch_size = BATCH_SIZE
    for obj in source_objects:
        obj_id = obj["_additional"]["id"]

        # Fetch full object data
        full_obj = client.data_object.get_by_id(obj_id, class_name=SOURCE_CLASS)
        if not full_obj:
            print(f"⚠️ Skipping {obj_id}, could not fetch properties.")
            continue

        properties = full_obj.get("properties", {})
        vector = full_obj.get("vector")  # Preserve vector if exists

        batch.add_data_object(
            data_object=properties,
            class_name=DEST_CLASS,
            uuid=obj_id,
            vector=vector  # Optional: Keep embeddings
        )

print("✅ Migration complete.")
