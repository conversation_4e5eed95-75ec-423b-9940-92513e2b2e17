import { useNavigate } from "react-router-dom";
import { useState } from "react";
import axios from "axios";

export default function AdminDashboard() {
  const nav = useNavigate();
  const [processing, setProcessing] = useState(false);

  const runPipeline = async () => {
    if (!window.confirm("Have ALL uploads finished? This will start the processing pipeline.")) return;
    setProcessing(true);
    try {
      const token = localStorage.getItem("access");
      await axios.post(
        "/api/tickets/process_uploads/",
        {},
        {
          headers: { Authorization: `Bearer ${token}` },
        }
      );
      alert("Pipeline Completed");
    } catch (err) {
      alert(err.response?.data?.detail || "Error starting process.");
    } finally {
      setProcessing(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem("userData");
    localStorage.removeItem("access");
    localStorage.removeItem("refresh");

    nav("/auth", { replace: true });

    setTimeout(() => {
      if (window.location.pathname !== "/auth") {
        window.location.href = "/auth";
      }
    }, 300);
  };

  return (
    <div
      style={{
        minHeight: "100vh",
        backgroundColor: "#1e1e2f", // deep slate blue
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        padding: "2rem",
      }}
    >
      <div
        style={{
          backgroundColor: "#2a2a40", // elegant dark card
          borderRadius: "12px",
          padding: "32px",
          width: "100%",
          maxWidth: "480px",
          boxShadow: "0 6px 24px rgba(0, 0, 0, 0.3)",
        }}
      >
        <h2
          style={{
            fontSize: "1.75rem",
            fontWeight: "600",
            color: "#f3f4f6",
            textAlign: "center",
            marginBottom: "24px",
            borderBottom: "1px solid #3b3b55",
            paddingBottom: "12px",
          }}
        >
          Admin Dashboard
        </h2>

        <div style={{ display: "flex", flexDirection: "column", gap: "16px" }}>
          <DashboardButton
            label="Uploads"
            color="#3b82f6"
            hover="#2563eb"
            onClick={() => nav("/uploads")}
          />
          <DashboardButton
            label="Prompt Templates"
            color="#10b981"
            hover="#059669"
            onClick={() => nav("/prompt-manager")}
          />
          <DashboardButton
            label="Escalated Tickets"
            color="#6b7280"
            hover="#d97706"
            onClick={() => nav("/admin/tickets")}
            disabled
          />
          <DashboardButton
            label="Admin Chatbot"
            color="#8b5cf6"
            hover="#7c3aed"
            onClick={() => nav("/admin/chatbot")}
          />
          <DashboardButton
            label="Usage Dashboard"
            color="#06b6d4"
            hover="#0891b2"
            onClick={() => nav("/usage")}
          />
          <DashboardButton
            label={processing ? "Processing..." : "Process Uploads"}
            color={processing ? "#6b7280" : "#ef4444"}
            hover={processing ? "#6b7280" : "#dc2626"}
            onClick={runPipeline}
            disabled={processing}
          />
          <DashboardButton
            label="Logout"
            color="#374151"
            hover="#1f2937"
            onClick={handleLogout}
          />
        </div>
      </div>
    </div>
  );
}

function DashboardButton({ label, color, hover, onClick, disabled = false }) {
  const [isHovered, setHovered] = useState(false);

  return (
    <button
      onClick={onClick}
      disabled={disabled}
      onMouseEnter={() => setHovered(true)}
      onMouseLeave={() => setHovered(false)}
      style={{
        padding: "12px 16px",
        borderRadius: "8px",
        backgroundColor: isHovered ? hover : color,
        color: "#f9fafb",
        fontSize: "1rem",
        fontWeight: 600,
        border: "none",
        cursor: disabled ? "not-allowed" : "pointer",
        transition: "background-color 0.2s ease-in-out",
      }}
    >
      {label}
    </button>
  );
}
