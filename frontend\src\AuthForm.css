/* Full-page container */
.auth-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #e0e7ff 0%, #fdfbff 50%, #e1e6ff 100%);
  font-family: "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
}

/* Auth box */
.auth-card {
  width: 100%;
  max-width: 420px;
  background: #ffffff;
  padding: 36px 40px;
  border-radius: 18px;
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.12);
  animation: fadeIn 0.6s ease;
  display: flex;
  flex-direction: column;
  max-height: 90vh;
  overflow-y: auto; /* scroll if needed */
}

/* Title */
.auth-title {
  text-align: center;
  font-size: 1.8rem;
  font-weight: 700;
  color: #283593;
  margin-bottom: 28px;
}

/* Error banner */
.auth-error {
  background: #ffe8e6;
  border: 1px solid #ffb4b0;
  color: #d32f2f;
  padding: 10px 14px;
  border-radius: 8px;
  font-size: 0.875rem;
  text-align: center;
  margin-bottom: 20px;
}

/* Inputs */
.auth-input {
  width: 100%;
  padding: 12px 14px;
  margin-bottom: 18px;
  font-size: 0.95rem;
  border: 1px solid #c7c9df;
  border-radius: 10px;
  transition: border-color 0.25s, box-shadow 0.25s;
  background-color: #f0f4ff;
  box-sizing: border-box;
}

.auth-input:focus {
  border-color: #5b6eff;
  box-shadow: 0 0 0 3px rgba(91, 110, 255, 0.25);
  outline: none;
}

/* Submit button */
.auth-button {
  width: 100%;
  padding: 12px;
  margin-top: 10px;
  font-size: 1rem;
  font-weight: 600;
  color: #ffffff;
  background: linear-gradient(135deg, #5b6eff 0%, #3c4fe0 100%);
  border: none;
  border-radius: 10px;
  cursor: pointer;
  transition: opacity 0.25s, transform 0.25s;
  box-sizing: border-box;
}

.auth-button:hover:not([disabled]) {
  opacity: 0.9;
  transform: translateY(-1px);
}

.auth-button:active:not([disabled]) {
  transform: translateY(0);
}

.auth-button[disabled] {
  background: #9fa8da;
  cursor: not-allowed;
}

/* Fieldset */
.auth-fieldset {
  border: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
}

/* Bottom switch text */
.auth-switch {
  margin-top: auto;
  text-align: center;
  font-size: 0.9rem;
  color: #555b6e;
  padding-top: 18px;
}

/* Switch link as button */
.auth-switch-link {
  background: none;
  border: none;
  color: #3c4fe0;
  cursor: pointer;
  font-weight: 600;
  text-decoration: underline;
  margin-left: 4px;
  transition: color 0.2s;
}

.auth-switch-link:hover {
  color: #283593;
}

/* Smooth fade animation */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(12px); }
  to   { opacity: 1; transform: translateY(0);   }
}

/* Mobile responsive scroll - only scroll if viewport height <= 700px */
@media (max-height: 700px) {
  .auth-card {
    max-height: 90vh;
  }
}
